export interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone?: string;
  avatar_url?: string;
  is_host: boolean;
  created_at: string;
  updated_at: string;
}

export interface Listing {
  id: string;
  host_id: string;
  title: string;
  description: string;
  property_type: 'apartment' | 'house' | 'condo' | 'villa' | 'cabin' | 'other';
  room_type: 'entire_place' | 'private_room' | 'shared_room';
  max_guests: number;
  bedrooms: number;
  bathrooms: number;
  price_per_night: number;
  address: string;
  city: string;
  state: string;
  country: string;
  latitude?: number;
  longitude?: number;
  amenities: string[];
  images: string[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Booking {
  id: string;
  listing_id: string;
  guest_id: string;
  check_in: string;
  check_out: string;
  guests: number;
  total_price: number;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  created_at: string;
  updated_at: string;
}

export interface AuthRequest extends Request {
  user?: User;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  phone?: string;
}

export interface CreateListingRequest {
  title: string;
  description: string;
  property_type: string;
  room_type: string;
  max_guests: number;
  bedrooms: number;
  bathrooms: number;
  price_per_night: number;
  address: string;
  city: string;
  state: string;
  country: string;
  amenities: string[];
}

export interface CreateBookingRequest {
  listing_id: string;
  check_in: string;
  check_out: string;
  guests: number;
}
