-- StayFinder Database Schema for Supabase

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table
CREATE TABLE users (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    phone VARCHAR(20),
    avatar_url TEXT,
    is_host BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Listings table
CREATE TABLE listings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    host_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    property_type VARCHAR(50) NOT NULL CHECK (property_type IN ('apartment', 'house', 'condo', 'villa', 'cabin', 'other')),
    room_type VARCHAR(50) NOT NULL CHECK (room_type IN ('entire_place', 'private_room', 'shared_room')),
    max_guests INTEGER NOT NULL CHECK (max_guests > 0),
    bedrooms INTEGER NOT NULL CHECK (bedrooms >= 0),
    bathrooms DECIMAL(3,1) NOT NULL CHECK (bathrooms >= 0),
    price_per_night DECIMAL(10,2) NOT NULL CHECK (price_per_night > 0),
    address TEXT NOT NULL,
    city VARCHAR(100) NOT NULL,
    state VARCHAR(100) NOT NULL,
    country VARCHAR(100) NOT NULL,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    amenities TEXT[] DEFAULT '{}',
    images TEXT[] DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Bookings table
CREATE TABLE bookings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    listing_id UUID NOT NULL REFERENCES listings(id) ON DELETE CASCADE,
    guest_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    check_in DATE NOT NULL,
    check_out DATE NOT NULL,
    guests INTEGER NOT NULL CHECK (guests > 0),
    total_price DECIMAL(10,2) NOT NULL CHECK (total_price > 0),
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'cancelled', 'completed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT check_dates CHECK (check_out > check_in)
);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_listings_host_id ON listings(host_id);
CREATE INDEX idx_listings_city ON listings(city);
CREATE INDEX idx_listings_property_type ON listings(property_type);
CREATE INDEX idx_listings_price ON listings(price_per_night);
CREATE INDEX idx_listings_active ON listings(is_active);
CREATE INDEX idx_bookings_listing_id ON bookings(listing_id);
CREATE INDEX idx_bookings_guest_id ON bookings(guest_id);
CREATE INDEX idx_bookings_dates ON bookings(check_in, check_out);
CREATE INDEX idx_bookings_status ON bookings(status);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_listings_updated_at BEFORE UPDATE ON listings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bookings_updated_at BEFORE UPDATE ON bookings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE listings ENABLE ROW LEVEL SECURITY;
ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;

-- Users can read their own data
CREATE POLICY "Users can view own profile" ON users
    FOR SELECT USING (auth.uid() = id);

-- Users can update their own data
CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.uid() = id);

-- Anyone can view active listings
CREATE POLICY "Anyone can view active listings" ON listings
    FOR SELECT USING (is_active = true);

-- Hosts can manage their own listings
CREATE POLICY "Hosts can manage own listings" ON listings
    FOR ALL USING (auth.uid() = host_id);

-- Users can view bookings they're involved in
CREATE POLICY "Users can view own bookings" ON bookings
    FOR SELECT USING (
        auth.uid() = guest_id OR 
        auth.uid() IN (SELECT host_id FROM listings WHERE id = listing_id)
    );

-- Users can create bookings
CREATE POLICY "Users can create bookings" ON bookings
    FOR INSERT WITH CHECK (auth.uid() = guest_id);

-- Users can update bookings they're involved in
CREATE POLICY "Users can update relevant bookings" ON bookings
    FOR UPDATE USING (
        auth.uid() = guest_id OR 
        auth.uid() IN (SELECT host_id FROM listings WHERE id = listing_id)
    );

-- Insert sample data
INSERT INTO users (email, password_hash, first_name, last_name, is_host) VALUES
('<EMAIL>', '$2a$10$example_hash_1', 'John', 'Doe', true),
('<EMAIL>', '$2a$10$example_hash_2', 'Jane', 'Smith', false),
('<EMAIL>', '$2a$10$example_hash_3', 'Mike', 'Wilson', true);

-- Note: You'll need to replace the password_hash values with actual bcrypt hashes
-- and update the user IDs in the listings below with the actual UUIDs generated

-- Sample listings (replace user IDs with actual UUIDs after users are created)
-- INSERT INTO listings (host_id, title, description, property_type, room_type, max_guests, bedrooms, bathrooms, price_per_night, address, city, state, country, amenities, images) VALUES
-- ('user_id_1', 'Cozy Downtown Apartment', 'Beautiful apartment in the heart of the city', 'apartment', 'entire_place', 4, 2, 1.5, 120.00, '123 Main St', 'New York', 'NY', 'USA', '{"wifi", "kitchen", "air_conditioning"}', '{"https://example.com/image1.jpg"}'),
-- ('user_id_2', 'Luxury Beach House', 'Stunning beachfront property with ocean views', 'house', 'entire_place', 8, 4, 3.0, 350.00, '456 Ocean Ave', 'Miami', 'FL', 'USA', '{"wifi", "pool", "beach_access", "parking"}', '{"https://example.com/image2.jpg"}');
