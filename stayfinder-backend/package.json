{"name": "stayfinder-backend", "version": "1.0.0", "description": "StayFinder Backend API", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon src/index.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["airbnb", "booking", "properties"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@supabase/supabase-js": "^2.50.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.13", "@types/node": "^24.0.1", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}