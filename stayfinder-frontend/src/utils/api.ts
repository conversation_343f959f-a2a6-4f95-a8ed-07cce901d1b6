import axios from 'axios';
import { 
  User, 
  Listing, 
  Booking, 
  RegisterData, 
  LoginData, 
  SearchFilters,
  CreateListingData,
  CreateBookingData 
} from '../types';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  register: async (userData: RegisterData): Promise<{ user: User; token: string }> => {
    const response = await api.post('/auth/register', userData);
    return response.data;
  },

  login: async (credentials: LoginData): Promise<{ user: User; token: string }> => {
    const response = await api.post('/auth/login', credentials);
    return response.data;
  },

  getProfile: async (): Promise<{ user: User }> => {
    const response = await api.get('/auth/profile');
    return response.data;
  },
};

// Listings API
export const listingsAPI = {
  getListings: async (filters?: SearchFilters): Promise<{ listings: Listing[] }> => {
    const response = await api.get('/listings', { params: filters });
    return response.data;
  },

  getListingById: async (id: string): Promise<{ listing: Listing }> => {
    const response = await api.get(`/listings/${id}`);
    return response.data;
  },

  createListing: async (listingData: CreateListingData): Promise<{ listing: Listing }> => {
    const response = await api.post('/listings', listingData);
    return response.data;
  },

  updateListing: async (id: string, listingData: Partial<CreateListingData>): Promise<{ listing: Listing }> => {
    const response = await api.put(`/listings/${id}`, listingData);
    return response.data;
  },

  deleteListing: async (id: string): Promise<void> => {
    await api.delete(`/listings/${id}`);
  },

  getMyListings: async (): Promise<{ listings: Listing[] }> => {
    const response = await api.get('/listings/host/my-listings');
    return response.data;
  },
};

// Bookings API
export const bookingsAPI = {
  createBooking: async (bookingData: CreateBookingData): Promise<{ booking: Booking }> => {
    const response = await api.post('/bookings', bookingData);
    return response.data;
  },

  getMyBookings: async (): Promise<{ bookings: Booking[] }> => {
    const response = await api.get('/bookings/my-bookings');
    return response.data;
  },

  getHostBookings: async (): Promise<{ bookings: Booking[] }> => {
    const response = await api.get('/bookings/host-bookings');
    return response.data;
  },

  updateBookingStatus: async (id: string, status: string): Promise<{ booking: Booking }> => {
    const response = await api.patch(`/bookings/${id}/status`, { status });
    return response.data;
  },
};

export default api;
