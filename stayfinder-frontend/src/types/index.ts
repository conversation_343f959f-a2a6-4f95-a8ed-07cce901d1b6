export interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone?: string;
  avatar_url?: string;
  is_host: boolean;
  created_at: string;
  updated_at: string;
}

export interface Listing {
  id: string;
  host_id: string;
  title: string;
  description: string;
  property_type: 'apartment' | 'house' | 'condo' | 'villa' | 'cabin' | 'other';
  room_type: 'entire_place' | 'private_room' | 'shared_room';
  max_guests: number;
  bedrooms: number;
  bathrooms: number;
  price_per_night: number;
  address: string;
  city: string;
  state: string;
  country: string;
  latitude?: number;
  longitude?: number;
  amenities: string[];
  images: string[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
  users?: {
    first_name: string;
    last_name: string;
    avatar_url?: string;
  };
}

export interface Booking {
  id: string;
  listing_id: string;
  guest_id: string;
  check_in: string;
  check_out: string;
  guests: number;
  total_price: number;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  created_at: string;
  updated_at: string;
  listings?: {
    title: string;
    city: string;
    state: string;
    images: string[];
    users?: {
      first_name: string;
      last_name: string;
    };
  };
  users?: {
    first_name: string;
    last_name: string;
    email: string;
  };
}

export interface AuthContextType {
  user: User | null;
  token: string | null;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => void;
  loading: boolean;
}

export interface RegisterData {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  phone?: string;
}

export interface LoginData {
  email: string;
  password: string;
}

export interface SearchFilters {
  city?: string;
  max_guests?: number;
  min_price?: number;
  max_price?: number;
  property_type?: string;
  check_in?: string;
  check_out?: string;
}

export interface CreateListingData {
  title: string;
  description: string;
  property_type: string;
  room_type: string;
  max_guests: number;
  bedrooms: number;
  bathrooms: number;
  price_per_night: number;
  address: string;
  city: string;
  state: string;
  country: string;
  amenities: string[];
}

export interface CreateBookingData {
  listing_id: string;
  check_in: string;
  check_out: string;
  guests: number;
}
